'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Palette,
  Smartphone,
  BarChart3,
  Zap,
  Users,
  Globe,
  Star,
  Check,
  ArrowRight,
  Sparkles,
  Heart,
  Shield,
  Rocket
} from "lucide-react";
import { useAuthStore } from "@/lib/stores/auth-store";
import { useEffect } from "react";

export default function Home() {
  const { isAuthenticated, checkAuth } = useAuthStore();

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-indigo-50">
      {/* Navigation */}
      <nav className="border-b bg-white/90 backdrop-blur-md sticky top-0 z-50 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                LinkVibe
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" className="hidden sm:inline-flex text-gray-600 hover:text-purple-600">
                Features
              </Button>
              <Button variant="ghost" className="hidden sm:inline-flex text-gray-600 hover:text-purple-600">
                Pricing
              </Button>
              {isAuthenticated ? (
                <Link href="/dashboard">
                  <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg">
                    Go to Dashboard
                  </Button>
                </Link>
              ) : (
                <>
                  <Link href="/auth">
                    <Button variant="outline" className="border-purple-200 text-purple-600 hover:bg-purple-50">
                      Sign In
                    </Button>
                  </Link>
                  <Link href="/auth">
                    <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg">
                      Start for Free
                    </Button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 sm:py-32">

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <Badge className="mb-6 bg-gradient-to-r from-sky-100 to-blue-100 text-blue-700 border-blue-200 shadow-sm">
              ✨ LinkVibe for Creators
            </Badge>
            <h1 className="text-4xl sm:text-6xl lg:text-7xl font-bold tracking-tight text-gray-900 mb-6 leading-tight">
              <div>Create Your Perfect</div>
              <div className="bg-gradient-to-r from-purple-600 via-pink-600 to-indigo-600 bg-clip-text text-transparent">
                Bio Link Page
              </div>
              <div>in Minutes</div>
            </h1>
            <p className="text-xl sm:text-2xl text-gray-600 max-w-3xl mx-auto mb-10 leading-relaxed">
              Build stunning, personalized landing pages with our intuitive builder.
              No coding required – just pure creativity and professional results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              {isAuthenticated ? (
                <Link href="/dashboard">
                  <Button
                    className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 shadow-xl hover:shadow-2xl transition-all duration-300"
                  >
                    Go to Dashboard
                    <ArrowRight className="ml-2 w-4 h-4" />
                  </Button>
                </Link>
              ) : (
                <Link href="/auth">
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-lg px-8 py-6 h-auto shadow-xl hover:shadow-2xl transition-all duration-300"
                  >
                    Start for Free
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </Button>
                </Link>
              )}
            </div>
            <p className="text-sm text-gray-500 mt-4">
              No credit card required • 5-minute setup • Cancel anytime
            </p>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 sm:py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
              Everything You Need to{" "}
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Stand Out
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Powerful features designed to help creators, entrepreneurs, and influencers
              build their perfect online presence.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature 1: Easy to Use */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-purple-50 to-pink-50 hover:scale-105">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-4 mx-auto shadow-lg">
                  <Zap className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-xl font-semibold text-gray-900">Easy to Use</CardTitle>
                <CardDescription className="text-base text-gray-600">
                  Intuitive builder that lets you create stunning pages in minutes.
                  No technical skills required.
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Feature 2: Beautiful Templates */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-blue-50 to-cyan-50 hover:scale-105">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mb-4 mx-auto shadow-lg">
                  <Palette className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-xl font-semibold text-gray-900">Beautiful Templates</CardTitle>
                <CardDescription className="text-base text-gray-600">
                  Choose from professionally designed themes: Minimal, Dark Mode, Neon,
                  Pastel, and more.
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Feature 3: Mobile Optimized */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-green-50 to-emerald-50 hover:scale-105">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mb-4 mx-auto shadow-lg">
                  <Smartphone className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-xl font-semibold text-gray-900">Mobile Optimized</CardTitle>
                <CardDescription className="text-base text-gray-600">
                  Perfect on every device. Your bio page looks stunning on mobile,
                  tablet, and desktop.
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Feature 4: Analytics Dashboard */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-orange-50 to-red-50 hover:scale-105">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mb-4 mx-auto shadow-lg">
                  <BarChart3 className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-xl font-semibold text-gray-900">Analytics Dashboard</CardTitle>
                <CardDescription className="text-base text-gray-600">
                  Track visits, clicks, and engagement. Understand your audience
                  with detailed insights.
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Feature 5: Social Integration */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-violet-50 to-purple-50 hover:scale-105 relative">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-violet-500 to-purple-500 rounded-2xl flex items-center justify-center mb-4 mx-auto shadow-lg">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-xl font-semibold text-gray-900">Social Integration</CardTitle>
                <CardDescription className="text-base text-gray-600">
                  Connect all your social platforms. Instagram, TikTok, YouTube,
                  Twitter, and more.
                </CardDescription>
                <Badge className="absolute top-4 right-4 bg-orange-100 text-orange-700 border-orange-200">
                  Coming Soon
                </Badge>
              </CardHeader>
            </Card>

            {/* Feature 6: Custom Domains */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-teal-50 to-blue-50 hover:scale-105 relative">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-teal-500 to-blue-500 rounded-2xl flex items-center justify-center mb-4 mx-auto shadow-lg">
                  <Globe className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-xl font-semibold text-gray-900">Custom Domains</CardTitle>
                <CardDescription className="text-base text-gray-600">
                  Use your own domain for a professional look. Build your brand
                  with custom URLs.
                </CardDescription>
                <Badge className="absolute top-4 right-4 bg-orange-100 text-orange-700 border-orange-200">
                  Coming Soon
                </Badge>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Templates Preview Section */}
      <section className="py-20 sm:py-32 bg-gradient-to-br from-sky-50 via-blue-50 to-indigo-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
              Choose Your{" "}
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Perfect Style
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              From minimal elegance to bold statements, find the template that matches your vibe.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {/* Minimal Template */}
            <Card className="group cursor-pointer border-2 hover:border-purple-300 transition-all duration-300 overflow-hidden hover:scale-105 shadow-lg">
              <div className="aspect-[3/4] bg-gradient-to-b from-white to-gray-50 p-6 flex flex-col items-center justify-center">
                <div className="w-16 h-16 bg-gray-200 rounded-full mb-4"></div>
                <div className="w-24 h-3 bg-gray-300 rounded mb-2"></div>
                <div className="w-16 h-2 bg-gray-200 rounded mb-6"></div>
                <div className="space-y-3 w-full">
                  <div className="w-full h-8 bg-gray-100 rounded-lg"></div>
                  <div className="w-full h-8 bg-gray-100 rounded-lg"></div>
                  <div className="w-full h-8 bg-gray-100 rounded-lg"></div>
                </div>
              </div>
              <CardHeader className="pt-4 text-center">
                <CardTitle className="text-gray-900">Minimal</CardTitle>
                <CardDescription>Clean and professional</CardDescription>
              </CardHeader>
            </Card>

            {/* Dark Mode Template */}
            <Card className="group cursor-pointer border-2 hover:border-purple-300 transition-all duration-300 overflow-hidden hover:scale-105 shadow-lg">
              <div className="aspect-[3/4] bg-gradient-to-b from-gray-900 to-black p-6 flex flex-col items-center justify-center">
                <div className="w-16 h-16 bg-gray-700 rounded-full mb-4"></div>
                <div className="w-24 h-3 bg-gray-600 rounded mb-2"></div>
                <div className="w-16 h-2 bg-gray-700 rounded mb-6"></div>
                <div className="space-y-3 w-full">
                  <div className="w-full h-8 bg-gray-800 rounded-lg"></div>
                  <div className="w-full h-8 bg-gray-800 rounded-lg"></div>
                  <div className="w-full h-8 bg-gray-800 rounded-lg"></div>
                </div>
              </div>
              <CardHeader className="pt-4 text-center">
                <CardTitle className="text-gray-900">Dark Mode</CardTitle>
                <CardDescription>Sleek and modern</CardDescription>
              </CardHeader>
            </Card>

            {/* Neon Template */}
            <Card className="group cursor-pointer border-2 hover:border-purple-300 transition-all duration-300 overflow-hidden hover:scale-105 shadow-lg">
              <div className="aspect-[3/4] bg-gradient-to-b from-purple-900 via-pink-900 to-black p-6 flex flex-col items-center justify-center">
                <div className="w-16 h-16 bg-gradient-to-r from-cyan-400 to-pink-400 rounded-full mb-4 shadow-lg shadow-cyan-500/25"></div>
                <div className="w-24 h-3 bg-gradient-to-r from-cyan-400 to-pink-400 rounded mb-2"></div>
                <div className="w-16 h-2 bg-purple-400 rounded mb-6"></div>
                <div className="space-y-3 w-full">
                  <div className="w-full h-8 bg-gradient-to-r from-cyan-500 to-pink-500 rounded-lg shadow-lg shadow-cyan-500/25"></div>
                  <div className="w-full h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg shadow-lg shadow-purple-500/25"></div>
                  <div className="w-full h-8 bg-gradient-to-r from-pink-500 to-orange-500 rounded-lg shadow-lg shadow-pink-500/25"></div>
                </div>
              </div>
              <CardHeader className="pt-4 text-center">
                <CardTitle className="text-gray-900">Neon</CardTitle>
                <CardDescription>Bold and vibrant</CardDescription>
              </CardHeader>
            </Card>
          </div>

          <div className="text-center mt-12">
            <Button size="lg" variant="outline" className="text-lg px-8 py-6 h-auto border-purple-200 text-purple-600 hover:bg-purple-50">
              View All Templates
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 sm:py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
              Simple,{" "}
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Transparent Pricing
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Start for free, upgrade when you're ready. No hidden fees, no surprises.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Free Tier */}
            <Card className="border-2 border-gray-200 hover:border-purple-300 transition-all duration-300 relative shadow-lg hover:shadow-xl">
              <CardHeader className="text-center pb-8">
                <CardTitle className="text-2xl font-bold text-gray-900">Free</CardTitle>
                <div className="mt-4">
                  <span className="text-4xl font-bold text-gray-900">$0</span>
                  <span className="text-gray-600">/month</span>
                </div>
                <CardDescription className="text-base mt-2">
                  Perfect for getting started
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span className="text-gray-700">1 bio page</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span className="text-gray-700">5 basic templates</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span className="text-gray-700">Basic analytics</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span className="text-gray-700">Mobile optimized</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span className="text-gray-700">LinkVibe subdomain</span>
                  </div>
                </div>
                <Button className="w-full mt-8" variant="outline" disabled>
                  Coming Soon
                </Button>
              </CardContent>
            </Card>

            {/* Creator Tier */}
            <Card className="border-2 border-purple-300 hover:border-purple-400 transition-all duration-300 relative shadow-xl hover:shadow-2xl">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-1 shadow-lg">
                  Most Popular
                </Badge>
              </div>
              <CardHeader className="text-center pb-8 pt-8">
                <CardTitle className="text-2xl font-bold text-gray-900">Creator</CardTitle>
                <div className="mt-4">
                  <span className="text-4xl font-bold text-gray-900">$9</span>
                  <span className="text-gray-600">/month</span>
                </div>
                <CardDescription className="text-base mt-2">
                  For serious creators and businesses
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span className="text-gray-700">Unlimited bio pages</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span className="text-gray-700">All premium templates</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span className="text-gray-700">Advanced analytics</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span className="text-gray-700">Custom domains</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span className="text-gray-700">Priority support</span>
                  </div>
                  <div className="flex items-center">
                    <Check className="w-5 h-5 text-green-500 mr-3" />
                    <span className="text-gray-700">Remove LinkVibe branding</span>
                  </div>
                </div>
                <Button className="w-full mt-8 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 shadow-lg" disabled>
                  Coming Soon
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600">
              All plans include a 14-day free trial. No credit card required.
            </p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 sm:py-32 bg-gradient-to-r from-purple-600 via-pink-600 to-indigo-600 relative overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-40 h-40 bg-white/10 rounded-full blur-xl"></div>
          <div className="absolute bottom-10 right-10 w-60 h-60 bg-white/10 rounded-full blur-xl"></div>
        </div>

        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6">
            Ready to Build Your Bio Link Page?
          </h2>
          <p className="text-xl text-purple-100 mb-10 max-w-2xl mx-auto">
            Join thousands of creators who are already using LinkVibe to showcase their work
            and grow their audience.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            {isAuthenticated ? (
              <Link href="/dashboard">
                <Button
                  size="lg"
                  className="bg-white text-purple-600 hover:bg-gray-100 text-lg px-8 py-6 h-auto shadow-xl hover:shadow-2xl transition-all duration-300"
                >
                  Go to Dashboard
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
            ) : (
              <Link href="/auth">
                <Button
                  size="lg"
                  className="bg-white text-purple-600 hover:bg-gray-100 text-lg px-8 py-6 h-auto shadow-xl hover:shadow-2xl transition-all duration-300"
                >
                  Start for Free
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
            )}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Brand */}
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold">LinkVibe</span>
              </div>
              <p className="text-gray-400 max-w-md">
                The easiest way to create beautiful, professional bio link pages.
                Build your online presence in minutes, not hours.
              </p>
            </div>

            {/* Product */}
            <div>
              <h3 className="font-semibold mb-4 text-white">Product</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Features</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Templates</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Pricing</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Analytics</a></li>
              </ul>
            </div>

            {/* Company */}
            <div>
              <h3 className="font-semibold mb-4 text-white">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Support</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>
          </div>

          <Separator className="my-8 bg-gray-700" />

          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 LinkVibe. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
